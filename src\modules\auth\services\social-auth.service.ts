import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
// import { UserRepository } from '../repositories/user.repository'; // REMOVED: UserRepository đã được xóa
import { SocialAccountRepository } from '../repositories/social-account.repository';
import { PermissionRepository } from '../repositories/permission.repository';
import { SocialLoginDto, SocialRegisterDto } from '../dto/social-auth.dto';
import { UserStatus } from '../enum/user-status.enum';
import { AppException } from '@/common/exceptions/app.exception';
import { TokenType } from '../guards/jwt.util';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import {
  UserLoginResponseDto,
  UserResponseDto,
} from '../dto/user-response.dto';
import {
  ISocialAuthProvider,
  SocialUserInfo,
} from './social/social-provider.interface';
import { GoogleAuthService } from './social/google-auth.service';
import { FacebookAuthService } from './social/facebook-auth.service';
import { ZaloAuthService } from './social/zalo-auth.service';
import { EncryptionService } from '@shared/services/encryption.service';
import { SocialProvider } from '../enum';

/**
 * Service xử lý xác thực qua mạng xã hội
 */
@Injectable()
export class SocialAuthService {
  private readonly logger = new Logger(SocialAuthService.name);
  private readonly socialProviders: Map<SocialProvider, ISocialAuthProvider>;

  constructor(
    // private readonly userRepository: UserRepository, // REMOVED: UserRepository đã được xóa
    private readonly socialAccountRepository: SocialAccountRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly googleAuthService: GoogleAuthService,
    private readonly facebookAuthService: FacebookAuthService,
    private readonly zaloAuthService: ZaloAuthService,
  ) {
    // Khởi tạo map các nhà cung cấp mạng xã hội
    this.socialProviders = new Map<SocialProvider, ISocialAuthProvider>();
    this.socialProviders.set(SocialProvider.GOOGLE, this.googleAuthService);
    this.socialProviders.set(SocialProvider.FACEBOOK, this.facebookAuthService);
    this.socialProviders.set(SocialProvider.ZALO, this.zaloAuthService);
  }

  /**
   * Đăng nhập bằng mạng xã hội
   * @param loginDto Thông tin đăng nhập
   * @returns Token và thông tin người dùng
   */
  async login(
    loginDto: SocialLoginDto,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    try {
      // Lấy service tương ứng với nhà cung cấp
      const provider = this.getSocialProvider(loginDto.provider);
      if (!provider) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_SOCIAL_PROVIDER,
          `Nhà cung cấp mạng xã hội không được hỗ trợ: ${loginDto.provider}`,
        );
      }

      // Lấy thông tin người dùng từ mạng xã hội
      const socialUserInfo = await provider.getUserInfo(loginDto.accessToken);

      // Tìm tài khoản mạng xã hội trong hệ thống
      const socialAccount = await this.socialAccountRepository.findBySocialId(
        loginDto.provider,
        socialUserInfo.id,
      );

      // Nếu không tìm thấy tài khoản, trả về lỗi
      if (!socialAccount) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_ACCOUNT_NOT_FOUND,
          `Tài khoản ${loginDto.provider} chưa được liên kết với hệ thống. Vui lòng đăng ký trước.`,
        );
      }

      // Tìm người dùng tương ứng
      // REMOVED: UserRepository đã được xóa, cần sử dụng EmployeeRepository
      // const user = await this.userRepository.findById(socialAccount.userId);
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Chức năng đăng nhập social đã được chuyển sang Employee system. Vui lòng liên hệ admin.',
      );
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Kiểm tra trạng thái tài khoản
      if (user.status !== UserStatus.ACTIVE) {
        throw new AppException(
          AUTH_ERROR_CODE.ACCOUNT_NOT_VERIFIED,
          'Tài khoản chưa được kích hoạt hoặc đã bị khóa.',
        );
      }

      // Cập nhật thông tin tài khoản mạng xã hội
      await this.socialAccountRepository.update(socialAccount.id, {
        accessToken: this.encryptionService.encrypt(loginDto.accessToken),
        updatedAt: Date.now(),
      });

      // Lấy danh sách quyền của người dùng
      const permissions = await this.permissionRepository.getUserPermissions(
        user.id,
      );

      // Tạo JWT token
      const payload = {
        id: user.id,
        sub: user.id,
        email: user.email,
        name: null, // user.fullName không còn tồn tại
        typeToken: TokenType.ACCESS,
        type: 'EMPLOYEE' as const,
        tenantId: user.tenantId,
        permissions: permissions,
      };

      const accessToken = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: this.configService.get<string>('JWT_EXPIRATION_TIME', '1d'),
      });

      // Trả về token và thông tin người dùng (không bao gồm mật khẩu)
      const { password, ...userData } = user;

      // Chuyển đổi dữ liệu entity thành DTO
      const userResponseDto = new UserResponseDto(userData as any);

      const response: UserLoginResponseDto = {
        accessToken,
        user: userResponseDto,
        permissions: permissions,
      };

      return ApiResponseDto.success(response);
    } catch (error) {
      this.logger.error(`Error in social login: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        `Đăng nhập bằng ${loginDto.provider} thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Đăng ký bằng mạng xã hội
   * @param registerDto Thông tin đăng ký
   * @returns Token và thông tin người dùng
   */
  async register(
    registerDto: SocialRegisterDto,
  ): Promise<ApiResponseDto<UserLoginResponseDto>> {
    try {
      // Lấy service tương ứng với nhà cung cấp
      const provider = this.getSocialProvider(registerDto.provider);
      if (!provider) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_SOCIAL_PROVIDER,
          `Nhà cung cấp mạng xã hội không được hỗ trợ: ${registerDto.provider}`,
        );
      }

      // Lấy thông tin người dùng từ mạng xã hội
      const socialUserInfo = await provider.getUserInfo(
        registerDto.accessToken,
      );

      // Kiểm tra xem tài khoản mạng xã hội đã tồn tại chưa
      const existingSocialAccount =
        await this.socialAccountRepository.findBySocialId(
          registerDto.provider,
          socialUserInfo.id,
        );

      if (existingSocialAccount) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_ACCOUNT_EXISTS,
          `Tài khoản ${registerDto.provider} đã được liên kết với hệ thống. Vui lòng đăng nhập.`,
        );
      }

      // Tạo username nếu không được cung cấp
      let username = registerDto.username;
      if (!username) {
        // Tạo username từ email hoặc tên hiển thị
        if (socialUserInfo.email) {
          username = socialUserInfo.email.split('@')[0];
        } else if (socialUserInfo.displayName) {
          username = socialUserInfo.displayName
            .toLowerCase()
            .replace(/\s+/g, '');
        } else {
          username = `user_${Date.now()}`;
        }
      }

      // Lấy email từ thông tin mạng xã hội nếu không được cung cấp
      const email = registerDto.email || socialUserInfo.email;
      if (!email) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_EMAIL_REQUIRED,
          'Email là bắt buộc để đăng ký tài khoản',
        );
      }

      // Kiểm tra xem email đã tồn tại chưa
      // REMOVED: UserRepository đã được xóa, cần sử dụng EmployeeRepository
      // const existingEmail = await this.userRepository.findByEmail(email);
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        'Chức năng đăng ký social đã được chuyển sang Employee system. Vui lòng liên hệ admin.',
      );
      if (existingEmail) {
        throw new AppException(
          AUTH_ERROR_CODE.SOCIAL_EMAIL_EXISTS,
          'Email đã được sử dụng bởi tài khoản khác',
        );
      }

      // Tạo tài khoản người dùng mới
      const now = Date.now();
      const userId = now;
      // REMOVED: UserRepository đã được xóa, code này không bao giờ chạy được
      // const user = await this.userRepository.create({
        id: userId,
        email,
        password: '', // Không cần mật khẩu cho tài khoản mạng xã hội
        // fullName: // Không còn tồn tại trong User entity
        //   registerDto.fullName ||
        //   socialUserInfo.displayName ||
        //   socialUserInfo.name,
        // avatarUrl: registerDto.avatarUrl || socialUserInfo.photoUrl, // Không còn tồn tại trong User entity
        status: UserStatus.ACTIVE, // Tài khoản mạng xã hội được kích hoạt ngay lập tức
        createdAt: now,
        tenantId: registerDto.tenantId, // Cần có tenantId
      });

      // Tạo liên kết với tài khoản mạng xã hội
      await this.socialAccountRepository.create({
        userId: user.id,
        provider: registerDto.provider,
        socialId: socialUserInfo.id,
        email: socialUserInfo.email,
        displayName: socialUserInfo.displayName || socialUserInfo.name,
        photoUrl: socialUserInfo.photoUrl,
        accessToken: this.encryptionService.encrypt(registerDto.accessToken),
        refreshToken: socialUserInfo.refreshToken
          ? this.encryptionService.encrypt(socialUserInfo.refreshToken)
          : null,
        tokenExpiresAt: socialUserInfo.tokenExpiresAt,
        profileData: socialUserInfo.rawProfile,
        createdAt: now,
        updatedAt: now,
      });

      // Lấy danh sách quyền của người dùng (mặc định là rỗng cho người dùng mới)
      const permissions = await this.permissionRepository.getUserPermissions(
        user.id,
      );

      // Tạo JWT token
      const payload = {
        id: user.id,
        sub: user.id,
        email: user.email,
        name: null, // user.fullName không còn tồn tại
        typeToken: TokenType.ACCESS,
        type: 'EMPLOYEE' as const,
        tenantId: user.tenantId,
        permissions: permissions,
      };

      const accessToken = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: this.configService.get<string>('JWT_EXPIRATION_TIME', '1d'),
      });

      // Trả về token và thông tin người dùng
      const userResponseDto = new UserResponseDto(user as any);

      const response: UserLoginResponseDto = {
        accessToken,
        user: userResponseDto,
        permissions: permissions,
      };

      return ApiResponseDto.success(response);
    } catch (error) {
      this.logger.error(
        `Error in social register: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        `Đăng ký bằng ${registerDto.provider} thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy service tương ứng với nhà cung cấp mạng xã hội
   * @param provider Nhà cung cấp mạng xã hội
   * @returns Service tương ứng hoặc null nếu không hỗ trợ
   */
  private getSocialProvider(
    provider: SocialProvider,
  ): ISocialAuthProvider | null {
    return this.socialProviders.get(provider) || null;
  }
}
