import { DataSource } from 'typeorm';
import { CompanyAccount } from '../../modules/auth/entities/company-account.entity';
import { Employee } from '../../modules/hrm/employees/entities/employee.entity';
import { CompanyStatus } from '../../modules/auth/enum/company-status.enum';
import { EmployeeStatus } from '../../modules/hrm/employees/enum/employee-status.enum';
import { EmploymentType } from '../../modules/hrm/employees/enum/employment-type.enum';
import { Gender } from '../../modules/hrm/employees/enum/gender.enum';
import * as bcrypt from 'bcrypt';

// Hàm tạo dữ liệu mẫu
export async function seedInitialData(dataSource: DataSource) {
  try {
    // Tạo dữ liệu mẫu cho company
    const companyRepository = dataSource.getRepository(CompanyAccount);
    const companies = [
      {
        id: 1,
        companyName: 'Công ty TNHH ABC',
        subdomain: 'abc',
        taxCode: '**********',
        companyEmail: '<EMAIL>',
        password: await bcrypt.hash('Company@123', 10),
        phoneNumber: '**********',
        address: '123 Đường ABC, Quận 1, TP.HCM',
        status: CompanyStatus.ACTIVE,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 2,
        companyName: 'Công ty TNHH XYZ',
        subdomain: 'xyz',
        taxCode: '**********',
        companyEmail: '<EMAIL>',
        password: await bcrypt.hash('Company@123', 10),
        phoneNumber: '**********',
        address: '456 Đường XYZ, Quận 2, TP.HCM',
        status: CompanyStatus.ACTIVE,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ];

    // Lưu companies vào database
    await companyRepository.save(companies);

    // Tạo dữ liệu mẫu cho employee (thay thế user)
    const employeeRepository = dataSource.getRepository(Employee);
    const employees = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('Admin@123', 10),
        accountStatus: 'ACTIVE',
        employeeCode: 'REDAI001',
        employeeName: 'Nguyễn Văn A',
        dateOfBirth: new Date('1990-01-01'),
        gender: Gender.MALE,
        departmentId: 1,
        jobTitle: 'Quản trị viên',
        employmentType: EmploymentType.FULL_TIME,
        status: EmployeeStatus.ACTIVE,
        hireDate: new Date(),
        createdAt: Date.now(),
        tenantId: 1,
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('Manager@123', 10),
        accountStatus: 'ACTIVE',
        employeeCode: 'REDAI002',
        employeeName: 'Trần Thị B',
        dateOfBirth: new Date('1992-02-02'),
        gender: Gender.FEMALE,
        departmentId: 1,
        jobTitle: 'Quản lý',
        employmentType: EmploymentType.FULL_TIME,
        status: EmployeeStatus.ACTIVE,
        hireDate: new Date(),
        createdAt: Date.now(),
        tenantId: 1,
      },
    ];

    // Lưu employees vào database
    await employeeRepository.save(employees);

    console.log('Đã thêm dữ liệu mẫu thành công!');
  } catch (error) {
    console.error('Lỗi khi thêm dữ liệu mẫu:', error);
    throw error;
  }
}
