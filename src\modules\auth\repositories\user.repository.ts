import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User } from '../entities/user.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { UserStatus } from '../enum/user-status.enum';

/**
 * Repository cho User
 * @deprecated User entity sẽ được gộp vào Employee entity. Sử dụng EmployeeRepository thay thế.
 * Repository này sẽ được xóa sau khi migration hoàn tất.
 */
@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {
    this.logger.warn('⚠️  UserRepository is DEPRECATED. Use EmployeeRepository instead. This will be removed after User-Employee merge.');
  }

  /**
   * Tìm người dùng theo email
   * @param email Email cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   * @deprecated Sử dụng EmployeeRepository.findByEmail() thay thế
   */
  async findByEmail(email: string): Promise<User | null> {
    this.logger.warn('⚠️  UserRepository.findByEmail() is DEPRECATED. Use EmployeeRepository.findByEmail() instead.');
    return this.repository.findOne({ where: { email } });
  }

  /**
   * Tìm người dùng theo ID
   * @param id ID cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   * @deprecated Sử dụng EmployeeRepository.findById() thay thế
   */
  async findById(id: number): Promise<User | null> {
    this.logger.warn('⚠️  UserRepository.findById() is DEPRECATED. Use EmployeeRepository.findById() instead.');
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm nhiều người dùng theo danh sách ID
   * @param ids Danh sách ID cần tìm
   * @returns Danh sách người dùng
   */
  async findByIds(ids: number[]): Promise<User[]> {
    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Tìm tất cả người dùng thuộc một phòng ban
   * @param departmentId ID phòng ban
   * @returns Danh sách người dùng thuộc phòng ban
   * @deprecated User entity không còn chứa departmentId, sử dụng Employee entity thay thế
   */
  async findByDepartmentId(
    tenantId: number,
    departmentId: number,
  ): Promise<User[]> {
    // User entity không còn departmentId, trả về mảng rỗng
    return [];
  }

  /**
   * Tìm người dùng theo employee ID
   * @param employeeId ID nhân viên
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   * @deprecated User entity không còn chứa employeeId, sử dụng Employee entity thay thế
   */
  async findByEmployeeId(employeeId: number): Promise<User | null> {
    // User entity không còn employeeId, trả về null
    return null;
  }

  /**
   * Đếm số lượng người dùng theo phòng ban
   * @returns Danh sách kết quả đếm theo phòng ban
   * @deprecated User entity không còn chứa departmentId, sử dụng Employee entity thay thế
   */
  async countByDepartments(
    tenantId: number,
  ): Promise<{ departmentId: number; count: number }[]> {
    // User entity không còn departmentId, trả về mảng rỗng
    return [];
  }

  /**
   * Đếm số lượng tài khoản người dùng có liên kết với nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Số lượng tài khoản có employeeId
   * @deprecated User entity không còn chứa employeeId, sử dụng Employee entity thay thế
   */
  async countUsersWithEmployee(tenantId: number): Promise<number> {
    // User entity không còn employeeId, trả về 0
    return 0;
  }

  /**
   * Tạo mới người dùng
   * @param data Dữ liệu người dùng
   * @returns Thông tin người dùng đã tạo
   */
  async create(data: Partial<User>): Promise<User> {
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  /**
   * Cập nhật thông tin người dùng
   * @param id ID người dùng cần cập nhật
   * @param data Dữ liệu cần cập nhật
   * @returns Thông tin người dùng đã cập nhật
   * @throws Error nếu không tìm thấy người dùng sau khi cập nhật
   */
  async update(id: number, data: Partial<User>): Promise<User> {
    await this.repository.update(id, data);
    const updatedUser = await this.findById(id);

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found after update`);
    }

    return updatedUser;
  }

  /**
   * Cập nhật trạng thái người dùng
   * @param id ID người dùng cần cập nhật
   * @param status Trạng thái mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateStatus(id: number, status: UserStatus): Promise<User> {
    return this.update(id, { status });
  }

  /**
   * Xóa người dùng (soft delete bằng cách cập nhật status thành DELETED)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID người dùng cần xóa
   * @returns True nếu xóa thành công
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.update(
      { id, tenantId },
      { status: UserStatus.DELETED },
    );
    return (result.affected ?? 0) > 0;
  }

  /**
   * Xóa nhiều người dùng (soft delete)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID người dùng cần xóa
   * @returns Số lượng người dùng đã xóa thành công
   */
  async bulkDelete(tenantId: number, ids: number[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.repository.update(
      { id: In(ids), tenantId },
      { status: UserStatus.DELETED },
    );
    return result.affected ?? 0;
  }

  /**
   * Tìm tất cả người dùng với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAll(
    tenantId: number,
    query: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
      status?: UserStatus;
      // Deprecated fields - User entity không còn chứa các field này
      departmentId?: number;
      employeeId?: number;
      hasEmployee?: boolean;
      userType?: string;
    } = {},
  ): Promise<PaginatedResult<User>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      // Deprecated fields - bỏ qua
      // departmentId,
      // employeeId,
      hasEmployee, // Giữ lại để filter user có/không có employee
      // userType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('user');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('user.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    // Filter by hasEmployee - join với bảng employees để kiểm tra
    if (hasEmployee !== undefined) {
      if (hasEmployee) {
        // Chỉ lấy user có employee (join với employees table)
        queryBuilder.innerJoin(
          'employees',
          'emp',
          'emp.createdBy = user.id AND emp.tenantId = :tenantId',
          { tenantId }
        );
      } else {
        // Chỉ lấy user không có employee (left join và check null)
        queryBuilder.leftJoin(
          'employees',
          'emp',
          'emp.createdBy = user.id AND emp.tenantId = :tenantId',
          { tenantId }
        );
        queryBuilder.andWhere('emp.id IS NULL');
      }
    }

    // Deprecated filters - User entity không còn chứa các field này
    // if (departmentId) {
    //   queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId });
    // }

    // if (employeeId) {
    //   queryBuilder.andWhere('user.employeeId = :employeeId', { employeeId });
    // }

    // if (userType) {
    //   queryBuilder.andWhere('user.userType = :userType', { userType });
    // }

    // Apply search if provided - chỉ tìm kiếm theo email
    if (search) {
      queryBuilder.andWhere(
        'user.email ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`user.${sortBy}`, sortDirection);

    // Đảm bảo distinct khi có join để tránh duplicate
    if (hasEmployee !== undefined) {
      queryBuilder.distinct(true);
    }

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
