import { Injectable } from '@nestjs/common';
import { EmployeeUserService } from '@modules/hrm/employees/services/employee-user.service';
import { EmployeeService } from '@modules/hrm/employees/services/employee.service';
import { UserService } from '@/modules/hrm/employees/services/user.service';
import { EmployeeStatus } from '@modules/hrm/employees/enum/employee-status.enum';
import { UserStatus } from '@modules/auth/enum/user-status.enum';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';

/**
 * Employee User Tools Provider
 * Cung cấp các tools liên quan đến quản lý nhân viên và tài khoản người dùng
 */
@Injectable()
export class EmployeeUserToolsProvider {
  constructor(
    private readonly employeeUserService: EmployeeUserService,
    private readonly employeeService: EmployeeService,
    private readonly userService: UserService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> tất cả employee user tools
   */
  getTools() {
    return [
      // Tạo nhân viên mới kèm tài khoản người dùng
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createDto = {
              userInfo: {
                username: _args.email.split('@')[0], // Tạo username từ email
                password: _args.password || 'TempPassword@123',
                email: _args.email,
                fullName: _args.fullName,
              },
              employeeName: _args.fullName,
              departmentId: _args.departmentId,
              jobTitle: _args.position,
              status:
                _args.status === 'active'
                  ? EmployeeStatus.ACTIVE
                  : _args.status === 'inactive'
                    ? EmployeeStatus.INACTIVE
                    : _args.status === 'terminated'
                      ? EmployeeStatus.TERMINATED
                      : EmployeeStatus.ACTIVE,
            };

            const result =
              await this.employeeUserService.createEmployeeWithUser(
                tenantId,
                createDto,
                userId,
              );
            return `Nhân viên và tài khoản đã được tạo thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Tạo nhân viên và tài khoản thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_employee_with_user',
          description: 'Tạo nhân viên mới kèm tài khoản người dùng',
          schema: z.object({
            fullName: z.string().nonempty().describe('Họ và tên nhân viên'),
            email: z.string().email().describe('Email nhân viên'),
            phoneNumber: z.string().optional().describe('Số điện thoại'),
            position: z.string().optional().describe('Chức vụ'),
            departmentId: z.number().optional().describe('ID phòng ban'),
            managerId: z.number().optional().describe('ID quản lý'),
            dateOfBirth: z
              .string()
              .optional()
              .describe('Ngày sinh (YYYY-MM-DD)'),
            gender: z
              .enum(['male', 'female', 'other'])
              .optional()
              .describe('Giới tính'),
            address: z.string().optional().describe('Địa chỉ'),
            insuranceNumber: z.string().optional().describe('Số bảo hiểm'),
            bankAccount: z
              .string()
              .optional()
              .describe('Số tài khoản ngân hàng'),
            bankName: z.string().optional().describe('Tên ngân hàng'),
            contractType: z
              .enum(['full_time', 'part_time', 'contract', 'internship'])
              .optional()
              .describe('Loại hợp đồng'),
            startDate: z
              .string()
              .optional()
              .describe('Ngày bắt đầu làm việc (YYYY-MM-DD)'),
            endDate: z
              .string()
              .optional()
              .describe('Ngày kết thúc hợp đồng (YYYY-MM-DD)'),
            salary: z.number().optional().describe('Mức lương'),
            status: z
              .enum(['active', 'inactive', 'terminated'])
              .optional()
              .default('active')
              .describe('Trạng thái nhân viên'),
            autoGeneratePassword: z
              .boolean()
              .optional()
              .default(true)
              .describe('Tự động tạo mật khẩu'),
            password: z
              .string()
              .optional()
              .describe('Mật khẩu (nếu không tự động tạo)'),
          }),
        },
      ),

      // Tạo tài khoản người dùng cho nhân viên
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createDto = {
              employeeId: _args.employeeId,
              email: _args.email,
              autoGeneratePassword: _args.autoGeneratePassword || true,
              password: _args.password,
            };

            const result = await this.employeeUserService.createUserForEmployee(
              tenantId,
              createDto,
              userId,
            );
            return `Tài khoản người dùng đã được tạo thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Tạo tài khoản người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_user_for_employee',
          description: 'Tạo tài khoản người dùng cho nhân viên đã tồn tại',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            email: z.string().email().describe('Email tài khoản'),
            autoGeneratePassword: z
              .boolean()
              .optional()
              .default(true)
              .describe('Tự động tạo mật khẩu'),
            password: z
              .string()
              .optional()
              .describe('Mật khẩu (nếu không tự động tạo)'),
          }),
        },
      ),

      // Gắn nhân viên với tài khoản người dùng
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const linkDto = {
              employeeId: _args.employeeId,
              userId: _args.targetUserId,
            };

            const result = await this.employeeUserService.linkEmployeeToUser(
              tenantId,
              linkDto,
              userId,
            );
            return `Nhân viên đã được gắn với tài khoản người dùng thành công:\n${JSON.stringify(result, null, 2)}`;
          } catch (error) {
            return `Gắn nhân viên với tài khoản thất bại: ${error.message}`;
          }
        },
        {
          name: 'link_employee_to_user',
          description: 'Gắn nhân viên với tài khoản người dùng đã tồn tại',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên'),
            targetUserId: z.number().describe('ID tài khoản người dùng'),
          }),
        },
      ),

      // Lấy danh sách nhân viên đã có tài khoản user
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 20,
              search: _args.search,
              email: _args.email,
              status:
                _args.status === 'active'
                  ? UserStatus.ACTIVE
                  : _args.status === 'inactive'
                    ? UserStatus.INACTIVE
                    : undefined,
              hasEmployee: true, // Chỉ lấy user có liên kết với employee
            };

            const result = await this.userService.findAllUsers(tenantId, query);
            return `Tìm thấy ${result.items.length} nhân viên đã có tài khoản (tổng ${result.meta.totalItems}):\n${JSON.stringify(result.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách nhân viên có tài khoản thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employees_with_user_accounts',
          description: 'Lấy danh sách nhân viên đã có tài khoản người dùng',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z
              .number()
              .optional()
              .default(20)
              .describe('Số lượng kết quả trên mỗi trang'),
            search: z
              .string()
              .optional()
              .describe('Tìm kiếm theo tên hoặc email'),
            email: z.string().optional().describe('Lọc theo email'),
            status: z
              .enum(['active', 'inactive'])
              .optional()
              .describe('Lọc theo trạng thái tài khoản'),
          }),
        },
      ),

      // Lấy danh sách tất cả người dùng với phân trang và lọc
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 20,
              search: _args.search,
              email: _args.email,
              status:
                _args.status === 'active'
                  ? UserStatus.ACTIVE
                  : _args.status === 'inactive'
                    ? UserStatus.INACTIVE
                    : _args.status === 'suspended'
                      ? UserStatus.SUSPENDED
                      : _args.status === 'pending'
                        ? UserStatus.PENDING
                        : undefined,
              hasEmployee: _args.hasEmployee,
              departmentId: _args.departmentId,
              employeeId: _args.employeeId,
            };

            const result = await this.userService.findAllUsers(tenantId, query);
            return `Tìm thấy ${result.items.length} người dùng (tổng ${result.meta.totalItems}):\n${JSON.stringify(result.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách người dùng thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_all_users',
          description: 'Lấy danh sách tất cả người dùng với phân trang và lọc',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z
              .number()
              .optional()
              .default(20)
              .describe('Số lượng kết quả trên mỗi trang'),
            search: z
              .string()
              .optional()
              .describe('Tìm kiếm theo tên hoặc email'),
            email: z.string().optional().describe('Lọc theo email cụ thể'),
            status: z
              .enum(['active', 'inactive', 'suspended', 'pending'])
              .optional()
              .describe('Lọc theo trạng thái tài khoản'),
            hasEmployee: z
              .boolean()
              .optional()
              .describe('Lọc theo việc có liên kết với nhân viên hay không'),
            departmentId: z
              .number()
              .optional()
              .describe('Lọc theo ID phòng ban'),
            employeeId: z.number().optional().describe('Lọc theo ID nhân viên'),
          }),
        },
      ),

      // Lấy thống kê tài khoản nhân viên
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );

            // Lấy tất cả user có employee
            const usersWithEmployee = await this.userService.findAllUsers(
              tenantId,
              {
                page: 1,
                limit: 1000,
                hasEmployee: true,
              },
            );

            // Lấy tất cả user không có employee
            const usersWithoutEmployee = await this.userService.findAllUsers(
              tenantId,
              {
                page: 1,
                limit: 1000,
                hasEmployee: false,
              },
            );

            const stats = {
              totalUsersWithEmployee: usersWithEmployee.meta.totalItems,
              totalUsersWithoutEmployee: usersWithoutEmployee.meta.totalItems,
              activeUsersWithEmployee: usersWithEmployee.items.filter(
                (u) => u.status === 'active',
              ).length,
              inactiveUsersWithEmployee: usersWithEmployee.items.filter(
                (u) => u.status === 'inactive',
              ).length,
            };

            return `📊 THỐNG KÊ TÀI KHOẢN NHÂN VIÊN:

👥 Tổng số tài khoản có liên kết nhân viên: ${stats.totalUsersWithEmployee}
🔗 Tổng số tài khoản chưa liên kết nhân viên: ${stats.totalUsersWithoutEmployee}
✅ Tài khoản nhân viên đang hoạt động: ${stats.activeUsersWithEmployee}
❌ Tài khoản nhân viên không hoạt động: ${stats.inactiveUsersWithEmployee}

📈 Tỷ lệ liên kết: ${((stats.totalUsersWithEmployee / (stats.totalUsersWithEmployee + stats.totalUsersWithoutEmployee)) * 100).toFixed(1)}%`;
          } catch (error) {
            return `Lấy thống kê tài khoản nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_employee_account_statistics',
          description:
            'Lấy thống kê về tài khoản nhân viên (số lượng tài khoản có/không có liên kết với nhân viên)',
          schema: z.object({}),
        },
      ),

      // Cập nhật thông tin cá nhân nhân viên
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto: any = {};
            if (_args.employeeName) updateDto.employeeName = _args.employeeName;
            if (_args.dateOfBirth) updateDto.dateOfBirth = _args.dateOfBirth;
            if (_args.gender) updateDto.gender = _args.gender;

            const employee = await this.employeeService.updatePersonalInfo(
              tenantId,
              _args.employeeId,
              updateDto,
              userId,
            );

            return `✅ Cập nhật thông tin cá nhân nhân viên thành công!

👤 THÔNG TIN ĐÃ CẬP NHẬT:
• ID nhân viên: ${employee.id}
• Tên nhân viên: ${employee.employeeName}
• Ngày sinh: ${employee.dateOfBirth ? new Date(employee.dateOfBirth).toLocaleDateString('vi-VN') : 'Chưa cập nhật'}
• Giới tính: ${employee.gender || 'Chưa cập nhật'}`;
          } catch (error) {
            return `❌ Cập nhật thông tin cá nhân nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee_personal_info',
          description:
            'Cập nhật thông tin cá nhân của nhân viên (họ tên, số điện thoại, địa chỉ, ngày sinh, giới tính)',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên cần cập nhật'),
            employeeName: z.string().optional().describe('Tên nhân viên'),
            dateOfBirth: z
              .number()
              .optional()
              .describe('Ngày sinh (timestamp)'),
            gender: z
              .enum(['male', 'female', 'other'])
              .optional()
              .describe('Giới tính'),
          }),
        },
      ),

      // Cập nhật thông tin hợp đồng nhân viên
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const tenantId = parseInt(
              config?.configurable?.['tenantId'] || '0',
            );
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const updateDto: any = {};
            if (_args.jobTitle) updateDto.jobTitle = _args.jobTitle;
            if (_args.employmentType)
              updateDto.employmentType = _args.employmentType;
            if (_args.hireDate) updateDto.hireDate = _args.hireDate;
            if (_args.terminationDate)
              updateDto.terminationDate = _args.terminationDate;

            const employee = await this.employeeService.updateContractInfo(
              tenantId,
              _args.employeeId,
              updateDto,
              userId,
            );

            return `✅ Cập nhật thông tin hợp đồng nhân viên thành công!

💼 THÔNG TIN HỢP ĐỒNG ĐÃ CẬP NHẬT:
• ID nhân viên: ${employee.id}
• Chức danh: ${employee.jobTitle || 'Chưa cập nhật'}
• Loại hình làm việc: ${employee.employmentType || 'Chưa cập nhật'}
• Ngày bắt đầu: ${employee.hireDate ? new Date(employee.hireDate).toLocaleDateString('vi-VN') : 'Chưa cập nhật'}
• Ngày kết thúc: ${employee.terminationDate ? new Date(employee.terminationDate).toLocaleDateString('vi-VN') : 'Chưa cập nhật'}`;
          } catch (error) {
            return `❌ Cập nhật thông tin hợp đồng nhân viên thất bại: ${error.message}`;
          }
        },
        {
          name: 'update_employee_contract_info',
          description:
            'Cập nhật thông tin hợp đồng của nhân viên (chức vụ, loại hợp đồng, ngày bắt đầu/kết thúc, lương)',
          schema: z.object({
            employeeId: z.number().describe('ID nhân viên cần cập nhật'),
            jobTitle: z.string().optional().describe('Chức danh công việc'),
            employmentType: z
              .enum([
                'full_time',
                'part_time',
                'contract',
                'temporary',
                'intern',
                'freelance',
              ])
              .optional()
              .describe('Loại hình làm việc'),
            hireDate: z
              .number()
              .optional()
              .describe('Ngày bắt đầu làm việc (timestamp)'),
            terminationDate: z
              .number()
              .optional()
              .describe('Ngày kết thúc hợp đồng (timestamp)'),
          }),
        },
      ),
    ];
  }
}
