import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Employee } from './entities/employee.entity';
import { EmployeeRepository } from './repositories/employee.repository';
import { EmployeeService } from './services/employee.service';
import { EmployeeController } from './controllers/employee.controller';
import { EmployeeUserService } from './services/employee-user.service';
import { EmployeeUserController } from './controllers/employee-user.controller';
import { EmployeePermissionService } from './services/employee-permission.service';
import { EmployeePermissionController } from './controllers/employee-permission.controller';
import { EmployeePermissionRepository } from './repositories/employee-permission.repository';
import { RoleService } from './services/role.service';
import { RoleController } from './controllers/role.controller';
import { EmployeeRoleController } from './controllers/employee-role.controller';
import { RoleRepository } from './repositories/role.repository';
import { UserService } from './services/user.service';
import { UserController } from './controllers/user.controller';
// // import { User } from '@/modules/auth/entities/user.entity'; // DEPRECATED: User entity sẽ được xóa // DEPRECATED: User entity sẽ được xóa
import { Role } from '@/modules/auth/entities/role.entity';
import { UserRole } from '@/modules/auth/entities/user-role.entity';
import { UserPermission } from '@/modules/auth/entities/user-permission.entity';
import { RolePermission } from '@/modules/auth/entities/role-permission.entity';
import { Permission } from '@/modules/auth/entities/permission.entity';
// import { UserRepository } from '@/modules/auth/repositories/user.repository'; // REMOVED: User entity đã được xóa
import { EncryptionService } from '@/shared/services/encryption.service';
import { OrgUnitsModule } from '../org-units/org-units.module';
import { EmailModule } from '@/modules/email/email.module';

/**
 * Module quản lý nhân viên
 */
@Global()
@Module({
  imports: [
    OrgUnitsModule,
    EmailModule,
    TypeOrmModule.forFeature([
      Employee,
      // User, // DEPRECATED: User entity sẽ được xóa
      Role,
      UserRole,
      UserPermission,
      RolePermission,
      Permission,
    ]),
  ],
  controllers: [
    EmployeeController,
    EmployeeUserController,
    EmployeePermissionController,
    EmployeeRoleController,
    RoleController,
    UserController,
  ],
  providers: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService,
    EmployeePermissionRepository,
    RoleService,
    RoleRepository,
    UserService, // DEPRECATED: Sẽ được thay thế bằng EmployeeService, nhưng vẫn cần cho UserController
    // UserRepository, // REMOVED: User entity đã được xóa
    EncryptionService,
  ],
  exports: [
    EmployeeService,
    EmployeeRepository,
    EmployeeUserService,
    EmployeePermissionService,
    EmployeePermissionRepository,
    RoleService,
    RoleRepository,
    UserService, // DEPRECATED: Sẽ được thay thế bằng EmployeeService, nhưng vẫn cần cho UserController
    // UserRepository, // REMOVED: User entity đã được xóa
  ],
})
export class EmployeesModule {}
